# WPF开发规范文档

## 目录
1. [项目结构规范](#1-项目结构规范)
2. [代码规范](#2-代码规范)
3. [自定义控件开发规范](#3-自定义控件开发规范)
4. [样式和主题规范](#4-样式和主题规范)
5. [行为(Behaviors)开发规范](#5-行为behaviors开发规范)
6. [MVVM架构规范](#6-mvvm架构规范)
7. [性能优化规范](#7-性能优化规范)
8. [测试规范](#8-测试规范)

---

## 1. 项目结构规范

### 1.1 解决方案结构

```
SolutionName/
├── SolutionName.sln                    # 解决方案文件
├── src/                                # 源代码目录
│   ├── SolutionName.Core/              # 核心业务逻辑库
│   ├── SolutionName.WPF/               # WPF主应用程序
│   ├── SolutionName.Controls/          # 自定义控件库
│   └── SolutionName.Common/            # 公共组件库
├── tests/                              # 测试项目目录
│   ├── SolutionName.Core.Tests/
│   ├── SolutionName.WPF.Tests/
│   └── SolutionName.Controls.Tests/
├── docs/                               # 文档目录
├── assets/                             # 资源文件目录
│   ├── Images/
│   ├── Icons/
│   └── Fonts/
└── tools/                              # 工具和脚本
```

### 1.2 WPF项目内部结构

```
ProjectName.WPF/
├── App.xaml                            # 应用程序入口
├── App.xaml.cs
├── Views/                              # 视图目录
│   ├── MainWindow.xaml
│   ├── MainWindow.xaml.cs
│   ├── Pages/                          # 页面视图
│   ├── UserControls/                   # 用户控件
│   └── Dialogs/                        # 对话框
├── ViewModels/                         # 视图模型
│   ├── MainWindowViewModel.cs
│   ├── Base/                           # 基类视图模型
│   └── Pages/
├── Models/                             # 数据模型
├── Services/                           # 服务层
│   ├── Interfaces/
│   └── Implementations/
├── Resources/                          # 资源文件
│   ├── Styles/                         # 样式资源
│   │   ├── Colors.xaml
│   │   ├── Fonts.xaml
│   │   ├── Controls/                   # 控件样式
│   │   └── Themes/                     # 主题
│   ├── Images/
│   ├── Icons/
│   └── Localization/                   # 本地化资源
├── Converters/                         # 值转换器
├── Behaviors/                          # 行为
├── Commands/                           # 命令
├── Helpers/                            # 辅助类
└── Extensions/                         # 扩展方法
```

### 1.3 命名约定

#### 文件和文件夹命名
- **文件夹**：使用PascalCase，如`ViewModels`、`UserControls`
- **XAML文件**：使用PascalCase，如`MainWindow.xaml`、`UserProfileView.xaml`
- **C#文件**：使用PascalCase，与类名保持一致
- **资源文件**：使用PascalCase，描述性命名，如`ButtonStyles.xaml`

#### 项目命名
- **解决方案**：`CompanyName.ProductName`
- **项目**：`CompanyName.ProductName.ProjectType`
- **程序集**：与项目名称保持一致

### 1.4 资源文件组织

#### 样式资源组织
```xml
<!-- App.xaml 中的资源引用 -->
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- 基础资源 -->
            <ResourceDictionary Source="Resources/Styles/Colors.xaml"/>
            <ResourceDictionary Source="Resources/Styles/Fonts.xaml"/>
            <ResourceDictionary Source="Resources/Styles/Brushes.xaml"/>
            
            <!-- 控件样式 -->
            <ResourceDictionary Source="Resources/Styles/Controls/Button.xaml"/>
            <ResourceDictionary Source="Resources/Styles/Controls/TextBox.xaml"/>
            
            <!-- 主题 -->
            <ResourceDictionary Source="Resources/Styles/Themes/Light.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

---

## 2. 代码规范

### 2.1 C# 代码风格

#### 命名规范
```csharp
// 类名：PascalCase
public class UserProfileViewModel : BaseViewModel
{
    // 私有字段：camelCase，以下划线开头
    private string _userName;
    private readonly IUserService _userService;
    
    // 公共属性：PascalCase
    public string UserName 
    { 
        get => _userName; 
        set => SetProperty(ref _userName, value); 
    }
    
    // 方法名：PascalCase
    public async Task LoadUserDataAsync()
    {
        // 局部变量：camelCase
        var userData = await _userService.GetUserAsync();
    }
    
    // 常量：PascalCase
    public const string DefaultUserName = "Guest";
    
    // 静态只读字段：PascalCase
    public static readonly string ApplicationName = "MyApp";
}
```

#### 依赖属性命名
```csharp
public class CustomButton : Button
{
    // 依赖属性：Property后缀
    public static readonly DependencyProperty CornerRadiusProperty =
        DependencyProperty.Register(
            nameof(CornerRadius),
            typeof(CornerRadius),
            typeof(CustomButton),
            new PropertyMetadata(new CornerRadius(0)));

    // CLR属性包装器
    public CornerRadius CornerRadius
    {
        get => (CornerRadius)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }
}
```

### 2.2 XAML 代码规范

#### 格式化规范
```xml
<!-- 良好的XAML格式 -->
<UserControl x:Class="MyApp.Views.UserControls.UserProfileControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="300" 
             d:DesignWidth="400">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0"
                   Text="{Binding Title}"
                   Style="{StaticResource HeaderTextStyle}"
                   Margin="0,0,0,16"/>
        
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto">
            <StackPanel Orientation="Vertical"
                        Spacing="8">
                <!-- 内容 -->
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
```

#### 属性排序规范
1. **x:Name** 或 **Name**
2. **Grid.Row**, **Grid.Column** 等附加属性
3. **Width**, **Height**, **Margin**, **Padding** 等布局属性
4. **Style**, **Template** 等样式属性
5. **数据绑定属性**
6. **事件处理器**

### 2.3 注释和文档标准

#### XML文档注释
```csharp
/// <summary>
/// 表示用户配置文件的视图模型
/// </summary>
/// <remarks>
/// 此类负责管理用户配置文件的显示和编辑逻辑
/// </remarks>
public class UserProfileViewModel : BaseViewModel
{
    /// <summary>
    /// 获取或设置用户名
    /// </summary>
    /// <value>用户的显示名称</value>
    public string UserName { get; set; }
    
    /// <summary>
    /// 异步加载用户数据
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>表示异步操作的任务</returns>
    /// <exception cref="ArgumentNullException">当userId为null时抛出</exception>
    public async Task LoadUserDataAsync(string userId)
    {
        if (userId == null)
            throw new ArgumentNullException(nameof(userId));
            
        // 实现逻辑
    }
}
```

#### XAML注释
```xml
<!-- 用户配置文件主容器 -->
<Grid>
    <!-- 头部区域：显示用户头像和基本信息 -->
    <StackPanel Grid.Row="0" Orientation="Horizontal">
        <!-- 用户头像 -->
        <Ellipse Width="64" Height="64">
            <!-- 头像图片画刷 -->
            <Ellipse.Fill>
                <ImageBrush ImageSource="{Binding AvatarSource}"/>
            </Ellipse.Fill>
        </Ellipse>
    </StackPanel>
</Grid>
```
